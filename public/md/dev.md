# Version Updates

#### v1.0.66 (11-07-2025)
- [x] Fix subscription logs
- [x] freezestartdate and unfreezedate datepicker clearable
- [x] consolidate invoice add sid

#### v1.0.65 (10-07-2025)
- [x] Subscription logs

#### v1.0.64 (08-07-2025)
- [x] Payment Receipt Notification Email to Customer — Enabled
- [x] Prebill Payment Receipt Notification Email to Tester — Testing

#### v1.0.63 (03-07-2025)
- [x] User receive payment email
![image](https://i.imgur.com/Qjn8hNp.jpeg)

#### v1.0.62 (02-07-2025)
- [x] Add new guide "Send bill to customer"

#### v1.0.61 (01-07-2025)
- [x] Add filter by plan on subscriptions

#### v1.0.60 (30-06-2025)
- [x] Remove all banking details from invoices on dashboard
- [x] Update billing index to exclude sensitive payment information
- [x] Add invalid IC user list report

#### v1.0.59 (10-06-2025)
- [x] Fix bill item for consolidate (#103)
- [x] Subscription report update (#102)
- [x] Recalculate function (#101)
- [x] Update bulkpayment to allow desired amount input (#100)
- [x] Image compression optimization (#99)

#### v1.0.58 (03-04-2025)
- [x] Subscription report guide (#98)
- [x] Sync bill guide (#97)
- [x] Fix alert modal (#96)
- [x] Add SID sync billings (#95)
- [x] Subscription filter guide + XLSX support (#94)
- [x] Zero special rate report updates (#92-93)
- [x] Guides index + back button (#91)
- [x] PDF generation optimization (#90)
- [x] Fix duplicate payments (#89)
- [x] Balance calculation update (#88)
- [x] Bill by dates update (#87)
- [x] Add graphs + fix duplicates (#85)

#### v1.0.57 (March 2025)
- [x] Fix consolidated billing (#84)
- [x] Consolidate guide (#83)
- [x] Fix 1-page layout (#82)
- [x] Consolidate invoices & fixes (#81)
- [x] Add month filter (#80)
- [x] Various fixes (#79)
- [x] JNX report + locale updates (#78)
- [x] Ticket summary feature (#77)

#### v1.0.58
- 🛠️ 03-02-2025 Update:
- [x] Bulk Payment

#### v1.0.57
- 🛠️ 21-01-2025 Update:
- [x] Agent Parent UI Added
- [x] Fix Subscription Group
- [x] Fix User Not Loading
- [x] Fix for monthly payment price issue

#### v1.0.56
- 🛠️ 10-12-2024 Update:
- [x] Fix cannot select hours & minutes in technical installation
- [x] Tickets report - hide average resolution time
- [x] Tickets report - click on ticket no will goto report details
- [x] Added sales scope

#### v1.0.55
- 🛠️ 6-12-2024 Update:
- [x] Plan form add 2 new fields (freeusage, contract)
- [x] Susbcription - changing plan will alert to update freeusage/contract or keep

#### v1.0.54
- 🛠️ 5-12-2024 Update:
- [x] Fix cannot select hours & minutes in technical installation
- [x] Tickets report - hide average resolution time
- [x] Tickets report - click on ticket no will goto report details
- [x] Added sales scope

#### v1.0.53
- 🛠️ 25-11-2024 Update:
- [x] Limit to 2 month (60 days for sales summary report)

#### v1.0.52
- 🛠️ 21-11-2024 Update:
- [x] Tickets auto show open on load
- [x] New Report - "Sales Summary Report"

#### v1.0.51
- 🛠️ 20-11-2024 Update:
- [x] Ticketing Report New Graph
- [x] New Report - "Upcoming Free Usage Report"

#### v1.0.50
- 🛠️ 15-10-2024 Update:
- [x] Prebills Dropdown and QR
- [x] Add date & building to tickets

#### v1.0.49
- 🛠️ 11-10-2024 Update:
- [x] Fix plan not showing

#### v1.0.48
- 🛠️ 4-11-2024 Update:
- [x] Allow admin update payment for last month payment till 7th of the month, *PS* admin need manually update the bill if do so
- [x] Fix Payment reactivate issues #2
- [x] Add user access to Support L1
- [x] Temporary remove api limit for Support L1
- [x] Dashboard - Adding - current month payment summary, total collected summary, suspended count

#### v1.0.47c
- 🛠️ 30-10-2024 Update:
- [x] Adding Ticket Filter to the system
- [x] Fix Payex payment re-active issues #1

#### v1.0.47b
- 🛠️ 24-09-2024 Update:
- [x] Adding "DK Senza" building to the system

#### v1.0.47a
- 🛠️ 23-09-2024 Update:
- [x] Adding "LSH33" building to the system

#### v1.0.46-47
- 🛠️ 23-09-2024 Update:
- [x] Fix Payment Blocking for Payex for Past month request
- [x] Adding Support L1 (API)
- [x] L1 Support module
- [x] Pre-Billing - TESTING MODULE
- [x] Enable View only for past month payment
- [x] Fix Payment Blocking for Payex for Past month request
- [x] Fix Payment button disable for past month payment
- [x] Fix Payment Invalid for Payex

#### v1.0.45c
- 🛠️ 18-09-2024 Update:
- [x] Payex auto resume service when full payment is made

#### v1.0.45b
- 🛠️ 4-9-2024 Update:
- [x] Hide Generate Bill @ subscriptions form

#### v1.0.45a
- 🛠️ 27-8-2024 Update:
- [x] Enable the billing payment modules for admin / dev

#### v1.0.45
- 🛠️ 26-8-2024 Update:
- [x] Week 3 Non Payment Notice & Suspend - Setup
- [x] Week 3 Non Payment Notice & Suspend - Run & Fix
- [x] Logo Change & Update

#### v1.0.44
- 🛠️ 16-8-2024 Update:

- [x] Payment button disable for past month payment
- [x] Week 3 Non Payment Notice & Suspend - Setup
- [x] Paymend Blocking for Payex for Past month request
- [x] VOIP Billing Test Modules 

#### v1.0.43
- 🛠️ 14-8-2024 Update:

- [x] fix search plan page
- [x] voip no
- [x] voip no - db
- [x] Week 2 Payment Reminder - Auto Mailer

#### v1.0.42
- 🛠️ 1-8-2024 Update:
- [x] Add SSM No Footer
- [x] Payex Return Page Update
- [x] Update New Payment Email Template
- [x] Adding new building - KLIA Residence
#### v1.0.41
- 🛠️ 16-7-2024 Update:
- [x] KTIC API Integration
- [x] KTIC API Update Status on every user status update
- [x] Latest Bill / Payment report
- [x] Latest Bill Report Ammend - Price

#### v1.0.40a
- 🛠️ 5-7-2024 Update:
- [x] Payex Payment Return Page redirect to thank you page only
- [x] subscription page - filter by name 
- [x] - report - subscription -- add period months
- [x] Fix existing customer select function


#### v1.0.40
- 🛠️ 27-6-2024 Update:
- [x] Latest Bill / Payment report
- [x] Subsciption tab - filter by user
- [x] User Setting page
- [x] Subsciption Filter update
- [x] api call on user tab fix

#### v1.0.39a
- 🛠️ 20-6-2024 Update:
- [x] Add new column in Tech installation order list
- [x] Add new item in Subscription Status menu

#### v1.0.39
- 🛠️ 18-6-2024 Update:
- [x] esc button to close modal
- [x] user dashboard page
- [x] Bulk Email Tools - Experimental Period
- [x] Updating [Policy & Privacy] [Terms & Conditions] according to highfi.com.my
- [x] Payment QR & link for FPX Payment Gateway
- [x] FPX Payment Gateway (Payex)
      - User can pay bill via FPX Link
      - User can pay bill via scanning the QR Code on the bill      

#### v1.0.38
- 🛠️ 10-6-2024 Update:
- [x] Adding Subscription status Filter to subscriptions report
- [x] Adding QRScan to Pay Via FPX to Bill
- [x] Update HighFi T&C and Privacy Policy

#### v1.0.37a
- 🛠️ 30-5-2024 Update:
- [x] Adding Subscription status to subscriptions report
- [x] Fix Subscription date issues in subscriptions report

#### v1.0.37
- 🛠️ 25-5-2024 Update:
- [x] Adding Subscription ID to subscriptions
- [x] Auto Generate Subscription ID during create
- [x] Subscription Report appending Subscription ID
- [x] Adding User Error Alert when Duplicate Email in Subscription Customer profile creations   

#### v1.0.36
- 🛠️ 26-4-2024 Update:
- [x] Adding Emailer Adding Emailer Functionality for bulk emails
- [x] Extract Emails from Users

#### v1.0.35b
- 🛠️ 22-4-2024 Update:
- [x] Adding Installation Fees to Subscriptions
![image](https://i.imgur.com/BGzzMR4.png)


#### v1.0.35a
- 🛠️ 20-4-2024 Update:
- [x] Adding contact, contact 2 search for Subscriptions
- [x] Update Plan loading for subscriptions
- [x] Adding New API restriction for KTIC
      - User Search

#### v1.0.35
- 🛠️ 11-4-2024 Update:
- [x] Bulk Update / Sync AAA function
- [x] UI Update for AAA Sync
- [x] Migration Username - Move / Sync

#### v1.0.34a
- 🛠️ 8-4-2024 Update:
- [x] Adding New Building - Sky Awani 4

#### v1.0.34
- 🛠️ 8-4-2024 Update:
- [x] AAA sync function

#### v1.0.33
- 🛠️ 5-4-2024 Update:
- [x] Adding Plan Search for subscribe func

#### v1.0.32
- 🛠️ 4-4-2024 Update:
- [x] Fixed Last Bill Date for Migration Users
- [x] Change Billing Way to - PrePaid - Pay first Calculation
- [x] ID Check duplicate / exist

#### v1.0.31
- 🛠️ 29-3-2024 Update:
- [x] Add Special Price For Subscribers
- [x] Import Subscriber - wt status migration

#### v1.0.3
- 🛠️ 28-3-2024 Update:
- [x] Adding Migration Status
- [x] Adding Migration Tools
- [x] Migration Previews & Update

#### v1.0.24a
- 🛠️ 18-5-2023 Merge:
- [x] Subscribtion Contract Month Fix
- [x] 2 New Report - Agent User List , Active User List
- [x] Report Monthly Bills - Remove Additional Info
#### v1.0.23d
- 🛠️ 28-4-2023 Merge:
- [x] BillNo Filter for Billing
#### v1.0.23c
- 🛠️ 5-4-2023 Merge:
- [x] Add new building - Riverville Residence
#### v1.0.23b
- 🛠️ 4-4-2023 Merge:
- [x] Adding Active Filter to adhoc report 1
#### v1.0.23a
- 🛠️ 4-4-2023 Merge:
- [x] add activation date column to Bill with Agent 2
- [x] Enable Add Payments for all Bills
- [x] SupPay Permission - Finance
- [x] SupPay Report page 2 Loop
- [x] Add Building Name in report & csv
- [x] Plans Updates - requested by JASON
- [x] Update Bill With Agent 2 Report 
- [x] Adding Agent name to adhoc report 1
#### v1.0.22
- 🛠️ 31-3-2023 Merge:
- [x] Subscriptions list #PENDING REMOVE 1
- [x] Bill Fix Functions
- [x] Subscriptions Filter at bill index
#### v1.0.21
- 🛠️ 10-3-2023 Merge: [d9ef8ace71fac30768714fdc782a7f9ed369cda2]
- [x] Payment Gateway Update
- [x] Year Discount Price Check

#### v1.0.20d
- 🛠️ 27-2-2023 Merge: [4a11d5c577c320e898faac5c62e66d8f0f22cf2d]
- [x] Adding New Building

#### v1.0.20c 
- 🛠️ 6-2-2023 Merge: [5d7875ff9116dd7936d30f7a0319e5029aa2f15d]
- [x] Adding New Free Usage User - Manually
- [x] Allow Free Use Plan with Special Building Code
#### v1.0.20b 
- 🛠️ 2-2-2023  Merge: [0bb8de3649f462e2258206b23d73e22bdfe8b89b]
- [x] Consolidate Bill Disable
- [x] AdHoc Report 1 , Adding Building Name 
- [x] New Free Plan Allocation

#### v1.0.20
- 3-1-2023
- [x] Update Database module - Hourly new Username sync with backend (New User) 
  - Restrict to max 50 pending installation user
#### v1.0.19c
- 21-12-2022
- [x] Adding 2 more new building to form - Green Park Residence, Kenwingston Cyberjaya

#### v1.0.19b
- 19-12-2022
- [x] Adding Deposit Amount to report bill with agent 2
- [x] Adding 2 more new building to form
#### v1.0.19a
- 1-12-2022
- [x] Date Format Fix - Bot Daily Send Bot
- [x] Username bot running Daily 6AM
#### v1.0.19
- 5-11-2022
- [x] Payment Attachment
- [x] add agent name into the billing report
#### v1.0.18b
-30-10-2022
- [x] Added New Building * Shaftsbury Cyberjaya
#### v1.0.18a
- 25-09-2022
- [x] Add Status = suspended --> to auto billing as well
- [x] Adding Agent, Plan Information (Bill Wt Agent Report)

#### v1.0.18
- 20-09-2022
- [x] Adding Building
    1. Shaftsbury Cyberjaya 
    2. Residensi Hijauan
- [x] Active User Report
- [x] Email Fix
#### v1.0.17
- 25-08-2022
- [x] Add manual payments only on last bill
  ![Last Payment Btn](https://i.imgur.com/yxLMAin.png)
- [x] SubPlace FPX Form

##### v1.0.16b
- 17-08-2022
- [x] Adding new building "Shaftsbury Residence Putrajaya"
- [x] Summary Payment Report - (demo)
- [x] Summary Dashboard added
  ![Summary Dashboard](https://i.imgur.com/KOTqqqC.png)
#### v1.0.16a
- 22-07-2022
- [x] Adding Msg error [admin create user]
#### v1.0.16
- 18-07-2022
- [x] Remove logout from login page for mobile
#### v1.0.15a
- 13-07-2022
- [x] [Auto ID generation v3] update interval to 60 minutes
- [x] Remove [Auto ID generation v2]
 
#### v1.0.15
- 12-07-2022
- [x] Request List & FuncCtrl Added
- [x] upcomingreport sort by date
#### v1.0.14a
- 08-07-2022
- [x] Adding ID Copy
- [x] Prevent Password Overwrite when update user 
- [x] Password security upgrade  
- [x] Filter Plan Fix - Valid Date 
- [x] Fix Activation Report
#### v1.0.13
- 06-07-2022
- [x] Equipment List Removal from Subscription (Requested 6Jul2022 #1)
- [x] Adding Serial No, GPON Serial No text field @ Subscription 
- [x] Backend - Remove Equipments Information
#### v1.0.12
- 06-07-2022
- [x] Print Installation Order - Adding Splitter , Serials, etc
- [x] Fix Billing Remove - (Only remove 1st item in the list)
- [x] Update Billing - Tax Default - 0
- [x] New Bot for Monitoring The Subscriptions ID Create
- [x] New Plan Added
#### v1.0.11
- 01-07-2022
- [x] JNX Finance Report
#### v1.0.10
- 25-06-2022
- [x] Adding Report - Upcoming Billing Report 
- [x] FIX : Subscriptions Loading Bugs
#### v1.0.9
- 24-06-2022
- [x] Adding Building Name Columns (Drop Down Options)
- [x] Billing PDF - Deposit Text Reposition to below Due Date
- [x] Print Order - Adding Voip Username & Password
#### v1.0.8
- 16-06-2022
- [x] Export Report - Billing List By Date CSV
#### v1.0.7
- 14-06-2022
- [x] API End for Subplace #f11
- [x] Add Splitter No in subscriptions

#### v1.0.6
- 11-06-2022
- [x] Billing format updated : Deposit position Update as requested 
- [x] Subplace Sandbox
- [x] Billing - Show Billno
#### v1.0.5
- 10-06-2022
- [x] VOIP ID generation Fix
- [x] Subscribe Form Fix - File Upload
- [x] Billing with voip 
#### v1.0.4
- 26-05-2022
- [x] Adding Port & Circuit No to subscriptions
- [x] Generate JNX Billing
#### v1.0.3
- 21-05-2022
- [x] Voip Auto ID Generation
- [x] Installation Order Print Fix
- [x] Addding New Email to Bcc when billing (APIDMA)

####   v1.0.2
- 06-05-2022
- [x] User ID & password small cap generation
- [x] Payment UI Update
- [x] Ticketing UI Update

####   v1.0.1
- 02-05-2022
- [x] Fix Color - Highfi
- [x] Admin Btn Fix
- [x] Add Update Logs
- [x] Auto Assign Agent - if user is an agent

   
#### Requested Pending
- [ ] Subplace Payment Gateway
- [ ] Agent - 3 Level Children
- [ ] Agent to create Sub Agent
- [ ] Adding New Subscription (VOIP) in middle of month ?
- [ ] Reset Password by Admin

#### Meeting 22 Jun 2022
- [ ] SST - Fix - Adjust & Deduct "Overpay"
- [x] Credit Adjustment Text in Corrections
- [x] Update and no need send Old Bill

#### Meeting Jun 2, 2022 08:00 PM 
- [x] Billing PDF - Deposit Text Reposition to below Due Date - v1.0.9
- [ ] Customer ID -  sequencial 9494616xxxxx #132
- [x] Show BillingNo in Billings - v1.0.6
- [x] Adding Building Name Columns (Drop Down Options) - v1.0.9
- [x] Payment Attachment - v1.0.7
- [x] Add Splitter No in subscriptions - v1.0.7
- [ ] Print Selected Bills - Bulk - Merge PDF
- [ ] Billing Report / Summary On / After Daily Routine
- [x] (KIV) JomPay -- Suggest --- Wait for confirmation
- [x] (KIV) Agent , Sub-Agent Roles - with commission calculations

#### Meeting Jun 27, 2022 04:30 PM
- 10k Rooms / Tenant 
- 1 House - 100MB (Owner) Modem
  - 3 Rooms
    - 20MB
    - 30MB
    - 50MB
- 

#### Bugs
- 16/7 [BUG] Support Ticketing - Number Nt Change / Update on every new Ticket Issue
- 16/7 [BUG] Support Ticketing - Customer default to customer 1 
- 16/7 [BUG] Support Ticketing - Customer Agent Default to agent 1 
- 16/7 [ADDON] Support Ticketing - Show Close Date
#### Request
- [ ] 2/6 Print Selected Bills - Bulk - Merge PDF
- [ ] 2/6 Customer ID -  sequencial 9494616xxxxx
- [ ] 2/6 Payment Attachment  
- [ ] 2/6 Billing Report / Summary On / After Daily Routine
- [ ] 5/7  &  12/7  -- Adding Sales Channel Indicator  : Jason
- [ ] 27/6 Multi Rooms / Subscriptions Management