<template>
  <div>
    <adminheader
        :title="$t('navigations.users')"
        :addFunction="isAdmin || isDev ? addUser : ''"
        :reloadFunction="reload"></adminheader>
    <div class="my-5 mx-6 border-b-2 border-gray-300 overflow-auto">
    	<ul class='flex cursor-pointer'>
          <li v-if="isSupportL1" class='py-2 px-6 rounded-t-lg mr-2' v-for="(item, index) in tagsSupportL1"
              :key="index"
              @click="currenttag = item"
              :class="currenttag === item ? 'bg-blue-500 text-white' : 'text-gray-500 bg-gray-200'">
            {{$t('c.' + item)}}
          </li>
          <li v-else
              class='py-2 px-6 rounded-t-lg mr-2'
              v-for="(item, indexTags) in tags"
              :key="indexTags"
              @click="currenttag = item"
              :class="currenttag === item ? 'bg-blue-500 text-white' : 'text-gray-500 bg-gray-200'">
            {{$t('c.' + item)}}
          </li>
    	</ul>
    </div>
    <dtablesearch :searchFunc="searchFunc"></dtablesearch>
    <div v-if="databases == null">
        <dloading />
    </div>
    <template v-else>
      <dtable
          :columns="columns"
          :data="databases"
          columnColor="white">
        <template v-slot:action="slotProps">
            <button :title="$t('c.edit')" @click="editRow(slotProps.item, slotProps.index)"  class="inline-block bg-blue-500 hover:bg-blue-700 text-white w-7 h-7 mr-2 rounded-full">
                <svgicon icon="edit" dclass="w-4 h-4 m-1 inline-block"/>
            </button>

            <button v-if="slotProps.item.customer" :title="$t('c.billing')" @click="bitemRow(slotProps.item)" class="inline-block bg-green-500 hover:bg-green-700 text-white w-7 h-7 mr-2 rounded-full">
              <svgicon icon="cash" dclass="w-4 h-4 m-1 inline-block"/>
            </button>

            <!-- <button :title="$t('c.delete')" @click="deleteRow(slotProps.item, slotProps.index)" class="inline-block bg-red-500 hover:bg-red-700 text-white w-7 h-7 mr-2 rounded-full">
              <svgicon icon="trash" dclass="w-4 h-4 m-1 inline-block"/>
            </button> -->
        </template>
      </dtable>
      <dpagination
          :total="databases && databases.total || 0"
          :page="table.page"
          :limit="table.limit"
          :pageChange="pageChange"
          defaultColor="blue"
      />
    </template>
    <template v-if="item">
        <dform
            :item="item"
            :cancel="cancelNow"
            :token="token"
            :profile="profile"
            :save="saveFunc"></dform>
    </template>
    <template v-if="bitem">
      <bindex :item="bitem" :cancelFunc="closeBitem"/>
    </template>
  </div>
</template>
<script lang="ts">
import { defineComponent, inject, computed } from 'vue'
import { auth2Store } from '../../../store/auth2-store'
import { crossStore } from '../../../store/cross-store'
import { userStore } from '../../../store/user-store'
import adminheader from '@/components/AdminHeader.vue'
import dtablesearch from '@/components/cvui/table/TableSearch.vue'
import dtable from '@/components/cvui/table/index.vue'
import dpagination from '@/components/cvui/table/Pagination.vue'
import dloading from '@/components/cvui/loading.vue'
import dform from './form.vue'
import svgicon from '@/components/cvui/svgcollection.vue'
import bindex from './billingindex.vue'

export default defineComponent({
  setup() {
    const authStore: any = inject("authStore")
    const authState = authStore.getState()
    const auth2State = auth2Store.getState()
    const userState = userStore.getState()
    return {
        token: computed(() => authState.token),
        authStore: authStore,
        authState: authState,
        profile: computed(() => auth2State.profile ),
        userStore: userStore,
        userState: userStore.getState()
    }
  },
  components: {
    adminheader,
    dtablesearch,
    dtable,
    dpagination,
    dloading,
    dform,
    svgicon,
    bindex
  },
  mounted () {
    this.reload()
  },
  data () {
    let item: any = null
    let deleteItem: any = null
    let itemStyle: any = {
      email: '',
      identityno: '',
      contact: '',
      contact2: '',
      name: '',
      nationality: 'MY',
      customer: true,
      support: false,
      installer: false,
      agent: false,
      mothersname: '',
      fathersname: '',
      gender: true,
      scopes: [],
      status: true,
      commissionrate: 0,
      commissiontype: 'fixed',
      agentParent: '',
      overwriterate: 0,
      commrepeat: 0,
      parentcommrepeat: 0
    }
    let table: any = {
        limit: 10,
        page: 1,
        keywords: '',
        status: true,
        customer: true
    }
    let tags: any = [
      'customer', 'installer', 'support', 'agent', 'all'
    ]
    let tagsSupportL1: any = [
      'customer', 'installer', 'support', 'agent', 'supportl1', 'all'
    ]
    let currenttag: string = 'customer'
    let keywords: string = ''
    let bitem: any = undefined
    return {
      item,
      deleteItem,
      itemStyle,
      table,
      tags,
      tagsSupportL1,
      currenttag,
      keywords,
      bitem,
    }
  },
  methods: {
    bitemRow (p: any) {
      this.bitem = p
    },
    closeBitem () {
      this.bitem = undefined
    },
    searchNow () {
      this.table.page = 1
      this.table.keywords = this.keywords
      this.loadDatabase()
    },
    searchFunc (p: string) {
      this.keywords = p
      this.searchNow()
    },
    reload () {
      this.table.page = 1
      this.table.keywords = ''
      this.keywords = ''
      this.loadDatabase()
    },
    loadDatabase () {
      let p = {...this.table, token: this.token }
      this.userStore.getUsers(p)
    },
    pageChange (p: any) {
      this.table.page = p
      this.loadDatabase()
    },
    initItem () {
      this.item = this.itemStyle
    },
    addUser () {
      this.initItem()
    },
    cancelNow () {
      this.item = null
      this.reload()
    },
    editRow (item: any, index: any) {
      if (!this.item) {
        if (item.ID) { this.item = Object.assign({id: item.ID}, item) }
        else { this.item = Object.assign({}, item) }

        this.item = JSON.parse(JSON.stringify(this.item))
        // for (let i = 0; i < Object.entries(this.itemStyle).length; i++) {
        //   let data = Object.entries(this.itemStyle)[i]
        //   if (Object.keys(this.item).indexOf(data[0]) === -1) {
        //     this.item[data[0]] = data[1]
        //   }
        // }
      }
    },
    duplicateRow (p: any, i: any) {
      this.item = Object.assign({}, p)
      delete this.item.id
      delete this.item.updated_at
      delete this.item.created_at
    },
    saveFunc (p: any) {
      if (p.id) {
        this.userStore.updateUser({ form: p, id: p.id, token: this.token })
      } else {
        this.userStore.createUserByAdmin({ form: p, token: this.token })
      }
      this.reload()
    },
    // deleteRow (p: any,i: any) {
    //   this.deleteItem = p
    //   crossStore.SetModalmsg({
    //     title: this.$t('c.deleteTitle'),
    //     msg: this.$t('c.confirmDelete') + ' ' + p.name + ' [' +p.email+ '] ?',
    //     proceedTxt:  this.$t('c.yes'),
    //     proceedFunc: () => { this.deleteNow(p.id)},
    //   })
    // },
    // deleteNow(id: any) {
    //   crossStore.SetModalmsg(null)
    //   userStore.deleteUser({ token: this.token, id })
    // }
  },
  computed: {
    columns () {
      return [
        { title: 'users.email', key: 'email', type: 'string', class: 'text-center' },
        { title: 'users.name', key: 'name', type: 'string', class: 'text-center' },
        { title: 'users.customer', key: 'customer', type: 'boolean', class: 'text-center' },
        { title: 'users.contact', key: 'contact', type: 'string', class: 'text-center' },
        // { title: 'users.agent', key: 'agent', type: 'boolean', class: 'text-center' },
        // { title: 'users.installer', key: 'installer', type: 'boolean', class: 'text-center' },
        // { title: 'users.support', key: 'support', type: 'boolean', class: 'text-center' },
        // { title: 'users.scopes', key: 'scopes', type: 'stringarray', class: 'text-center' },
        // { title: 'users.lastIP', key: 'lastIP', type: 'string', class: 'text-center' },
        // { title: 'users.lastLogin', key: 'lastLogin', type: 'timestamp', class: 'text-center w-1/5' },
        { title: 'c.action', key: 'action', type: 'action', class: 'text-center' },
      ]
    },
    databases () {
      return userStore.getState().users
    },
    userCreateByAdmin () {
      return userStore.getState().userCreateByAdmin
    },
    userCreateByAdminSuccess () {
      return userStore.getState().userCreateByAdminSuccess
    },
    userCreateByAdminError () {
      return userStore.getState().userCreateByAdminError
    },
    userUpdate () {
      return userStore.getState().userUpdate
    },
    userUpdateSuccess () {
      return userStore.getState().userUpdateSuccess
    },
    userUpdateError () {
      return userStore.getState().userUpdateError
    },
    // userDeleteSuccess () {
    //   return userStore.getState().userDeleteSuccess
    // },
    scopes () {
      let s: any = auth2Store.getState().profile
      
      return  s && s.scopes 
    },
    isSupportL1 () {
      let p: any = this.scopes
      if (p.length === 1 && p.includes('supportl1')) {
        return true;
      }
      return false;
    },
    isAdmin () {
      let p: any = this.scopes
      if (p.includes('admin')) {
        return true;
      }
      return false;
    },
    isDev () {
      let p: any = this.scopes
      if (p.includes('dev')) {
        return true;
      }
      return false;
    },
  },
  watch: {
    userCreateByAdminSuccess (p) {
      if (p) {
        this.item = null
        crossStore.SetNotmsg({
          title: this.$t('c.createTitle') + this.$t('users.user'),
          msg: this.$t('c.createSuccess'),
          type: 'success'
        })
      }
    },
    currenttag (p) {
      if (p) {
        delete this.table.customer
        delete this.table.agent
        delete this.table.support
        delete this.table.supportl1
        delete this.table.installer
        
        if (p == 'customer') {
          this.table.customer = true          
        } else if (p == 'agent') {
          this.table.agent = true
        }else if (p == 'support') {
          this.table.support = true
        }else if (p == 'installer') {
          this.table.installer = true
        } else if (p == 'supportl1') {
          this.table.supportl1 = true
        }
        this.reload()
      }
    },
    userCreateByAdminError (p) {
      if (p) {
        crossStore.SetModalmsg({
          title: this.$t('c.createTitle') + this.$t('users.user'),
          msg: p,
          type: 'error',
          proceedTxt: this.$t('c.okay')
        })
      }
    },
    userUpdateSuccess (p) {
      if (p) {
        this.item = null
        crossStore.SetNotmsg({
          title: this.$t('c.updateTitle') + this.$t('users.user'),
          msg: this.$t('c.updateSuccess'),
          type: 'success'
        })
      }
    },
    userUpdateError (p) {
      if (p) {
        crossStore.SetModalmsg({
          title: this.$t('c.updateTitle') + this.$t('users.user'),
          msg: this.$t('c.updateError'),
          type: 'error',
          proceedTxt: this.$t('c.okay')
        })
      }
    },
    // userDeleteSuccess (p) {
    //   if (p) {
    //     this.deleteItem = null
    //   }
    // }
  },
})
</script>
