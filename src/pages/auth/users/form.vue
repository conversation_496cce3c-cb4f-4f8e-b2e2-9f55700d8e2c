<template>
  <PopupModal
      defaultColor="blue"
      modalWidthPercent="90"
      :title="$t('users.formTitle')"
      :btnYesText="$t('c.submit')"
      :btnYesFunction="isAdmin || isDev ? submitNow : null"
      :btnNoText="$t('c.cancel')"
      :btnNoFunction="cancel"
      v-if="item">
    <div v-if="item.customer && showSubscription">
      <subscriptions :userid="item.id" :token="token" />
    </div>
    <div v-else>
      <div class='text-right my-5' v-if="item.id && item.customer">
        <div @click="toogleShowSubscriptions" class="text-xs inline-block px-2 py-1 bg-gray-100 rounded cursor-pointer">{{$t('users.showSubscription')}}</div>
      </div>
      <userform :profile="profile" :errs="errs" :item="item" :showMod="showMod" :token="token" />
    </div>
  </PopupModal>
</template>
<script lang="ts">
import { defineComponent } from 'vue'
import PopupModal from '@/components/cvui/Modal.vue'
import subscriptions from './subscriptions.vue'
import userform from './userform.vue'
import { auth2Store } from '../../../store/auth2-store';
export default defineComponent({
  props: {
    item: { type: Object, required: true },
    cancel: { type: Function },
    save: { type: Function },
    profile: { type: Object },
    token: {
      type: String,
      required: true
    }
  },
  computed: {
    scopes () {
      let s: any = auth2Store.getState().profile
      
      return  s && s.scopes 
    },
    isSupportL1 () {
      let p: any = this.scopes
      if (p.length === 1 && p.includes('supportl1')) {
        return true;
      }
      return false;
    },
    isAdmin () {
      let p: any = this.scopes
      if (p.includes('admin')) {
        return true;
      }
      return false;
    },
    isDev () {
      let p: any = this.scopes
      if (p.includes('dev')) {
        return true;
      }
      return false;
    },
  },
  mounted () {
    document.addEventListener('keydown', this.handleEscKey);
    if (this.item && this.item.password) {
      // delete
      delete this.item.password
    }
  },
  beforeUnmount() {
    document.removeEventListener('keydown', this.handleEscKey);
  },
  components: {
    PopupModal,    
    subscriptions,    
    userform
  },
  methods: {
    handleEscKey(event: KeyboardEvent) {
      if (event.key === 'Escape') {     
        if (this.cancel){
          this.cancel()
        }   
      }
    },
    toogleShowSubscriptions () {
      this.showSubscription = !this.showSubscription
    },
    validateForm () {
        let p = true
        if (!this.item.email || this.item.email.trim().length == 0) {
            this.errs['email'] = 'c.fieldRequired'
            p = false
        }
        if (!this.item.name || this.item.name.trim().length == 0) {
            this.errs['name'] = 'c.fieldRequired'
            p = false
        }
        if (!this.item.identityno || this.item.identityno.trim().length == 0) {
            this.errs['identityno'] = 'c.fieldRequired'
            p = false
        }
        this.item.commissionrate = parseFloat(this.item.commissionrate) || 0;
        this.item.commrepeat = parseFloat(this.item.commrepeat) || 0;
        this.item.overwriterate = parseFloat(this.item.overwriterate) || 0;
        this.item.parentcommrepeat = parseFloat(this.item.parentcommrepeat) || 0;
        return p
    },
    submitNow (e: any) {
        // e.preventDefault()
        if (this.validateForm()) {
            if (this.save != null) {
                this.save(this.item)
            }
        }
        return false
    },    
  },
  data () {
    let showSubscription = false
    const showMod: boolean = true
    let errs: any = {}
    return {
      errs,
      showSubscription,
      showMod,
    }
  }
})
</script>
