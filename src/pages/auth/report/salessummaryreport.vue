<template>
  <div>
      <adminheader
          :title="$t('report.salessummaryreport')"></adminheader>
      <div class="mx-2 my-2">
          <button @click="back" class="rounded bg-gray-200 hover:bg-gray-400 hover:text-white px-4">{{$t('c.back')}}</button>
      </div>
      <div class="mx-1 my-2 mr-2">
          <div class="w-full rounded bg-white p-3 shadow">
              <div class="text-center mt-5">
                  <svgCollection icon="report" dclass="inline-block w-5 h-5" />
                  {{$t('report.filterbybilldate')}}
                  <button title="Please select date not more than 61 days (2 month)." class="">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="ml-2 w-4 h-4">
                      <path stroke-linecap="round" stroke-linejoin="round" d="m11.25 11.25.041-.02a.75.75 0 0 1 1.063.852l-.708 2.836a.75.75 0 0 0 1.063.853l.041-.021M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9-3.75h.008v.008H12V8.25Z" />
                    </svg>
                  </button>
                  <div class="place-content-center mt-10">
                      <div class="mr-5 inline-block w-full lg:w-1/3 ">
                          <div class="text-xs my-1">{{$t('report.startdate')}}</div>
                          <DatePicker
                              v-model="startdate"
                              :clearable="true"
                              defaultColor="blue"></DatePicker>
                      </div>
                      <div class="mr-5 inline-block w-full lg:w-1/3 ">
                          <div class="text-xs my-1">{{$t('report.enddate')}}</div>
                          <DatePicker
                              v-model="enddate"
                              :clearable="true"
                              defaultColor="blue"></DatePicker>
                      </div>
                      <div class="inline-block w-full lg:w-1/5 mt-2">
                          <button @click="generateReport" class="mx-5 p-2 rounded bg-gray-200 text-sm px-5 cursor-pointer hover:bg-gray-500 hover:text-white">{{$t('report.generate')}}</button>
                      </div>                        
                  </div>
                  <div v-if="reportdata != undefined">
                      <div class="text-center p-10 text-sm" v-if="!reportdata.data || reportdata.count == 0">{{$t('report.emptylist')}}</div>
                      <div v-else class="mt-5">
                          <div v-if="loadingSubs"><loading /></div>
                          <template v-else>
                              <div class="text-right">
                                  <button @click="exportReport" class="rounded shadow px-5 py-1 bg-gray-300 hover:bg-gray-500 text-sm mb-5">{{$t('report.exportcsv')}}</button>
                              </div>
                              <div class="text-sm py-1 border-b grid grid-cols-9">
                                  <div>#</div>
                                  <div>Invoice No</div>
                                  <div>Subscriber Name</div>
                                  <div>Package</div>
                                  <div>Price</div>
                                  <div>Amount Current</div>
                                  <div>Amount Paid</div>
                                  <div>Amount BF</div>
                                  <div>Total Amount</div>                       
                              </div>
                               <div class="text-sm py-1 hover:bg-gray-100 border-b grid grid-cols-9" v-for="(l, li) in reportdata.data" :key="l._id">
                                <div>{{String(Number(li) + 1)}}</div>
                                  <div>{{l.billno}}</div>
                                  <div>{{l.customeritem[0].name}}</div>
                                  <div>{{l.items && l.items.length > 0 && showSubscribePlan(l.items[0].subscription)}}</div>
                                  <div>{{ showSubscribePlanPrice(l.items[0].subscription) }}</div>
                                  <div>{{formatmoney(l.amountcurrent)}}</div>
                                  <div>{{formatmoney(l.amountpaid)}}</div>
                                  <div>{{formatmoney(l.amountbf)}}</div>
                                  <div>{{formatmoney(l.totalamount)}}</div>
                              </div>                            
                          </template>
                      </div>
                  </div>
              </div>
          </div>
      </div>
  </div>
</template>
<script lang="ts">
import { defineComponent, computed, inject } from 'vue'
import adminheader from '@/components/AdminHeader.vue'
import svgCollection from '@/components/cvui/svgcollection.vue'
import DatePicker from '@/components/cvui/form/DatePicker.vue'
import moment from 'moment'
import { crossStore } from '../../../store/cross-store'
import { getBillingReport, getSubscription, getUserName, getPlan } from '../../../api'
import loading from '../../../components/cvui/loading.vue'
export default defineComponent({
  setup () {
      const authStore: any = inject("authStore")
      const authState = authStore.getState()
      return {
          token: computed(() => authState.token),
      }
  },
  components: {
      adminheader,
      svgCollection,
      DatePicker,
      loading
  },
  data() {
      let startdate: any = ''
      let enddate: any = ''
      let reportdata: any = undefined
      let charges: any = [
          ["JNX", 8],
          ["JNXBB", 4],
          ["JCV", 2],
      ]
      let subs: any = {}
      let agents: any = {}
      let plans: any = {}
      let loadingSubs: any = false
      return {
          startdate,
          enddate,
          reportdata,
          charges,
          subs,
          agents,
          plans,
          loadingSubs,
          fetchedSubscriptions: new Set<string>()
      }
  },
  methods: {
      count1 () {
          return (this.reportdata && this.reportdata.data && this.reportdata.data.length) || 0
      },
      back () {
          this.$router.back()
      },
    //   showSubscribeAgent (s: string) {
    //       if (this.subs[s]) {
    //           return (this.subs[s].agent && this.showAgent(this.subs[s].agent)) || 'No Agent'
    //       } else {
    //           return 'No Sub'
    //       }
    //   },
      showSubscribePlan (s: string) {
          if (this.subs[s]) {
              return (this.subs[s].plan && this.showPlan(this.subs[s].plan)) || 'No Plan'
          } else {
              return 'No Plan'
          }
      },
      showSubscribePlanPrice (s: string) {
          if (this.subs[s]) {
              return (this.subs[s].plan && this.showPlanPrice(this.subs[s].plan)) || 'No Plan'
          } else {
              return 'No Plan'
          }
      },
      showPlanPrice (s: string) {
          return (this.plans && this.plans[s] && this.plans[s].price) || 'No Plan Price' 
      },
      showPlan (s: string) {
          return (this.plans && this.plans[s] && this.plans[s].title) || 'No Plan' 
      },
      async getPlan(s: string) {
        if (!this.plans[s]) {
            try {
            const res = await getPlan({ token: this.token, id: s });
            if (res && res.data) {
                this.plans[res.data.id] = res.data; // Direct assignment for reactive objects
            }
            } catch (error) {
            console.error(`Error fetching plan ${s}:`, error);
            }
        }
        },

        async getSubscription(s: string) {
        if (this.fetchedSubscriptions.has(s)) return; // Skip if already fetched

        try {
            const res = await getSubscription({ token: this.token, id: s });
            if (res && res.data && res.data.id) {
            const subscriptionId = res.data.id;
            this.subs[subscriptionId] = res.data; // Direct assignment for reactive objects
            this.fetchedSubscriptions.add(subscriptionId); // Mark as fetched

            if (res.data.plan) {
                await this.getPlan(res.data.plan);
            }
            }
        } catch (error) {
            console.error(`Error fetching subscription ${s}:`, error);
        }
        },

        async loadSubs() {
        this.loadingSubs = true;
        const total = this.reportdata.data.length;

        // Use a Set to store unique subscription IDs
        const subscriptionIds = new Set<string>();

        // Collect all subscription IDs to fetch
        for (let i = 0; i < total; i++) {
            const td = this.reportdata.data[i];
            if (td.items && td.items.length > 0 && td.items[0].subscription) {
            subscriptionIds.add(td.items[0].subscription);
            }
        }

        // Fetch subscriptions in batches
        const subscriptionArray = Array.from(subscriptionIds);
        const batchSize = 50; // Number of concurrent requests

        for (let i = 0; i < subscriptionArray.length; i += batchSize) {
            const batch = subscriptionArray.slice(i, i + batchSize);
            await Promise.all(batch.map(id => this.getSubscription(id)));
            await new Promise(resolve => setTimeout(resolve, 1000)); // Throttle requests
        }

        this.loadingSubs = false;
        },

        async generateReport() {
        if (this.startdate && this.enddate) {
            const sdate = moment(this.startdate);
            const edate = moment(this.enddate);
            const daysDifference = edate.diff(sdate, 'days');

            if (daysDifference > 61) {
                console.error('Date range too large.');
                crossStore.SetNotmsg({
                    title: 'Date range too large.',
                    msg: 'Please select days no more than 61 days.',
                    type: 'error',
                })
            } else if (sdate.isAfter(edate)) {
                console.error('Start date is after end date.');
                crossStore.SetNotmsg({
                    title: 'Wrong date selection.',
                    msg: 'Start date is after end date.',
                    type: 'error',
                })
            } else {
            try {
                const res = await getBillingReport({
                token: this.token,
                startdate: this.formatdate2(sdate),
                enddate: this.formatdate2(edate),
                });
                this.reportdata = res;
                await this.loadSubs();
            } catch (error) {
                console.error('Error generating report:', error);
            }
            }
        } else {
            console.error('Start and end date are required.');
            crossStore.SetNotmsg({
                title: 'Wrong date selection.',
                msg: 'Start and end date are required.',
                type: 'error',
            })
        }
        },
      exportReport () {
          var Head: any = [['Invoice No', 'Subscriber Name', 'Package', 'Price', 'Amount Current', 'Amount Paid', 'Amount BF', 'Total Amount']];
 
          let row: any = this.reportdata.data
          let sdate = moment(this.startdate)
          let edate = moment(this.enddate)

          const addBackslashes = (str: any) => {
              // Add a backslash before commas, single quotes, and double quotes
              return str.replace(/\\/g, '\\\\') // escape existing backslashes
                      //.replace(/,/g, '\\,')
                      .replace(/'/g, '\\\'')
                      .replace(/"/g, '\\"');
          }
          if (row) {
              for (var item = 0; item < row.length; ++item) {
                  let l = row[item]

                  Head.push([
                      l.billno,
                      `"${addBackslashes(l.customeritem[0].name)}"`,
                      this.showSubscribePlan(l.items[0].subscription),
                      this.showSubscribePlanPrice(l.items[0].subscription),
                      l.amountcurrent,
                      l.amountpaid,
                      l.amountbf,
                      l.totalamount
                  ]);
                  // <div>{{l.billno}}</div>
  //                 <div>{{l.customeritem[0].name}}</div>
  //                 <div>{{l.items && l.items.length > 0 && showSubscribePlan(l.items[0].subscription)}}</div>
  //                 <div>{{ showSubscribePlanPrice(l.items[0].subscription) }}</div>
  //                 <div>{{formatmoney(l.amountcurrent)}}</div>
  //                 <div>{{formatmoney(l.amountpaid)}}</div>
  //                 <div>{{formatmoney(l.amountbf)}}</div>
  //                 <div>{{formatmoney(l.totalamount)}}</div>
              }
          }
          

          var csvRows = [];
          for (var cell = 0; cell < Head.length; ++cell) {
              csvRows.push(Head[cell].join(','));
          }
                      
          var csvString = csvRows.join("\n");
          let csvFile = new Blob([csvString], { type: "text/csv" });
          let downloadLink = document.createElement("a");
          downloadLink.download = `jnxreport_${sdate.format('YYYY-MM-DD')}_${edate.format('YYYY-MM-DD')}.csv`
          downloadLink.href = window.URL.createObjectURL(csvFile);
          downloadLink.style.display = "none";
          document.body.appendChild(downloadLink);
          downloadLink.click();
      },
      formatdate (p: any) {
          return moment(p).format('YYYY-MM-DD')
      },
      formatdate2 (p: any) {
          return p.format('YYYY-MM-DD')
      },
      formatmoney (p: any) {
          return p && p.toFixed(2)
      },
      copy (p: any) {
          crossStore.SetNotmsg({
              title: this.$t('c.textcopied'),
              msg: p,
              type: 'success',
          })
          navigator.clipboard.writeText(p)
      },
  }
})
</script>
