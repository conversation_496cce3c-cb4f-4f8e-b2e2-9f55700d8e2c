<template>
    <div>
        <adminheader
            :title="$t('navigations.activeuserreport')"></adminheader>
        <div class="mx-2 my-2">
            <button @click="back" class="rounded bg-gray-200 hover:bg-gray-400 hover:text-white px-4">{{$t('c.back')}}</button>
        </div>
        <div class="mx-1 my-2 mr-2">
            <div class="bg-white rounded border p-3">
              <div class="text-xs">Filter</div>
              <div class="text-right p-3 border rounded text-xs"><input type="checkbox" v-model="activeonly" /> Active Only</div>
              <div class="m-5">
                <button @click="generateReport" class="bg-gray-300 w-full rounded p-3 hover:bg-gray-500 hover:text-white">
                  {{$t('report.generate')}}
                </button>
                <button v-if="lists && lists.length > 0" @click="exportCSV" class="bg-gray-300 w-full rounded p-3 hover:bg-gray-500 hover:text-white mt-5">
                  {{$t('report.exportcsv')}}
                </button>
                
              </div>

              <div class="border-t border-b py-2">
                <div class="grid grid-cols-8 gap-4 text-xs font-bold">
                  <div>Building</div>
                  <div>Name</div>
                  <div>ActivationDate</div>
                  <div>Plan</div>
                  <div>FreeUsage</div>
                  <div>Price</div>
                  <div>Agent</div>
                  <div>Status</div>
                </div>
              </div>
              <div class="">
                <div class="border-b py-2" v-for="(vp,vi) in lists" :key="`s_ss_${addvu(vi)}`">
                  <div class="grid grid-cols-8 gap-4 text-xs">
                    <!-- <div>{{addvu(vi)}}. {{vp.address.building || 'The Grand Subang Jaya SS15'}} </div> -->
                    <!-- <div>{{vp.address.block}}-{{vp.address.level}}-{{vp.address.unit}}</div> -->
                    <div>{{strnum(vi)}}{{vp.address.building || 'The Grand Subang Jaya SS15'}}</div>
                    <div>{{vp.name}}</div>
                    <!-- <div>{{vp.contact}}</div> -->
                    <div>{{formatdate(vp.activationdate)}}</div>
                    <div>{{(packagePlan(vp.plan) && packagePlan(vp.plan).title) || '---'}}</div>
                    <div>{{vp.freeusage}}</div>
                    <div>{{(packagePlan(vp.plan) && packagePlan(vp.plan).price.toFixed(2)) || '--.--' }}</div>
                    <div>{{vp.agent && agents[vp.agent]&& agents[vp.agent].name}}</div>
                    <div>{{vp.statustxt}}</div>
                  </div>
                </div>
              </div>
            </div>
        </div>
    </div>
</template>
<script lang="ts">
import { defineComponent, inject, computed } from 'vue'
import moment from 'moment'
import adminheader from '@/components/AdminHeader.vue'
import svgCollection from '@/components/cvui/svgcollection.vue'
import { crossStore } from '../../../store/cross-store'
import { getSubscriptions, getPlan, getUserName } from '../../../api'
export default defineComponent({
  setup() {
    const authStore: any = inject("authStore")
    console.log(authStore)
    const authState = authStore.getState()
    
    return {
        token: computed(() => authState.token),
        authStore: authStore,
        authState: authState,
    }
  },
  components: {
    adminheader,
    svgCollection,
  },
  data () {
    let lists: any = undefined
    let plans: any = {}
    let planids: any = []
    let agentsid: any = []
    let agents: any = {}
    let activeonly: boolean = false
    return {
        lists,
        plans,
        planids,
        agentsid,
        agents,
        activeonly,
    }
  },
  methods: {
    getAgent (s: string) {
        if (!this.agents[s] && s.trim().length > 0) {
            getUserName({token: this.token, id: s}).then((res: any) => {
                if (res && res.id) {
                    this.agents[res.id] = res
                }
            })
        }
    },
    strnum (p: any) {
      return (p + 1).toString() + '.  '
    },
    getPlans () {
      for (var k = 0; k < this.planids.length; k++ ) {
        if (!this.plans[this.planids[k]]) {
          getPlan({token: this.token, id: this.planids[k]}).then((p: any) => {
            this.plans[p.data.id] = p.data
          })
        }   
      }
    },
    getAgents () {
      for (var k = 0; k < this.agentsid.length; k++ ) {
        if (!this.agents[this.agentsid[k]]) {
          this.getAgent(this.agentsid[k])
          // getPlan({token: this.token, id: this.agentsid[k]}).then((p: any) => {
          //   this.plans[p.data.id] = p.data
          // })
        }   
      }
    },
    formatdate (v: any) {
      return moment(v).format('YYYY-MM-DD')
    },
    packagePlan (p: any) {
      return this.plans[p] ? this.plans[p] : null
    },
    addvu (v: any) {
      return v + 1
    },
    generateReport () {
      // firstget
      this.lists = []
      this.doGetReport()
    },
    exportCSV () {
        let csvRows: any = []
        for (var i = 0; i < this.lists.length; i++) {
            let s: any = this.lists[i]
            csvRows.push(`${i+1},${s.address.building || 'The Grand Subang Jaya SS15'},${s.name},${this.formatdate(s.activationdate)}, ${this.plans[s.plan].title}, ${s.freeusage}, ${this.plans[s.plan].price}, ${s.agent && this.agents[s.agent] && this.agents[s.agent].name}`);
        }
                    
        var csvString = csvRows.join("\n");
        let csvFile = new Blob([csvString], { type: "text/csv" });
        let downloadLink = document.createElement("a");
        downloadLink.download = `userList.csv`
        downloadLink.href = window.URL.createObjectURL(csvFile);
        downloadLink.style.display = "none";
        document.body.appendChild(downloadLink);
        downloadLink.click();
    },
    doGetReport () {
      let limit: number = 20
      let v1: any = {
        token: this.token, skip: this.lists.length, limit: limit,// statustxt: 'active'
      }

      if (this.activeonly) {
        v1  = {
          token: this.token, skip: this.lists.length, limit: limit, statustxt: 'active'
        }
      }
      getSubscriptions(v1).then((res: any) => {
        this.lists= this.lists.concat(res.data)
        for (var k = 0; k < res.data.length; k++ ) {
          this.planids.push(res.data[k].plan)
          this.agentsid.push(res.data[k].agent)
        }
        this.planids = [...new Set(this.planids)]
        this.agentsid = [...new Set(this.agentsid)]
        this.getPlans()
        this.getAgents()
        if (res && res.total > this.lists.length) {
          setTimeout(this.doGetReport, 300)
        }
      })
    },
    back () {
        this.$router.back()
    },
    formatDate (p: any) {
        return (p&&moment(p).format('YYYY-MM-DD')) || '0000-00-00'
    },
    copy (p: any) {
        crossStore.SetNotmsg({
          title: this.$t('c.textcopied'),
          msg: p,
          type: 'success',
        })
        navigator.clipboard.writeText(p)
    },
  }
})
</script>